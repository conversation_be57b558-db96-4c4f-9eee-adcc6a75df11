//! # TAMTIL Core: A Type-Safe Distributed Actor System
//!
//! ## Abstract
//!
//! This paper presents the core type system for TAMTIL (Typed Actor Model with
//! Transactional Isolated Logs), a distributed actor framework that combines
//! Alice Ryhl's actor pattern with zero-copy serialization and embedded consensus.
//! The type system provides mathematical guarantees for distributed consistency
//! while maintaining developer productivity through a unified programming model.
//!
//! ## 1. Introduction
//!
//! ### 1.1 Problem Statement
//!
//! Modern distributed systems require three fundamental properties:
//! 1. **Type Safety**: Compile-time guarantees for distributed communication
//! 2. **Event Sourcing**: Deterministic state changes for distributed replication
//! 3. **Actor Isolation**: Memory safety and fault tolerance through message passing
//!
//! ### 1.2 Contributions
//!
//! This type system makes the following contributions:
//! 1. **Pure Functional Reactions**: All state changes expressed as memory operations
//! 2. **Scheduling as Event Sourcing**: Time-based execution through memory operations
//! 3. **Hierarchical Actor Addressing**: URL-based actor identification with automatic cleanup
//! 4. **Zero-Copy Type Preservation**: rkyv-based serialization without type erasure
//!
//! ## 2. Core Type System
//!
//! ### 2.1 Fundamental Types
//!
//! The type system is built on four fundamental abstractions that provide
//! mathematical guarantees for distributed actor systems.

use async_trait::async_trait;
use rkyv::{Archive, Deserialize, Serialize};
use std::marker::PhantomData;
use std::time::{Duration, SystemTime};

// ============================================================================
// SECTION 2.2: IDENTITY AND ADDRESSING
// ============================================================================

/// ## Definition 2.2.1: Node Identifier
///
/// A node identifier represents a unique computational unit in the distributed system.
/// Using u64 provides 2^64 possible nodes while maintaining cache-friendly performance.
pub type NodeId = u64;

/// ## Definition 2.2.2: Consensus Ballot
///
/// **Theorem**: Ballots provide total ordering across distributed nodes.
/// **Proof**: The tuple (n, pid) with lexicographic ordering ensures that for any
/// two ballots B1 = (n1, pid1) and B2 = (n2, pid2), exactly one of B1 < B2,
/// B1 > B2, or B1 = B2 holds, satisfying the trichotomy property.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct Ballot {
    /// Ballot number - monotonically increasing
    n: u64,
    /// Proposer node ID for tie-breaking
    pid: NodeId,
}

impl Ballot {
    /// Private constructor - use BallotBuilder instead
    fn new(n: u64, pid: NodeId) -> Self {
        Self { n, pid }
    }

    /// Get the ballot number
    pub fn n(&self) -> u64 {
        self.n
    }

    /// Get the proposer node ID
    pub fn pid(&self) -> NodeId {
        self.pid
    }
}

/// Builder for Ballot with validation and fluent interface
#[derive(Debug, Default)]
pub struct BallotBuilder {
    n: Option<u64>,
    pid: Option<NodeId>,
}

/// Error type for Ballot construction
#[derive(Debug, thiserror::Error)]
pub enum BallotBuildError {
    #[error("Ballot number (n) is required")]
    MissingN,
    #[error("Proposer ID (pid) is required")]
    MissingPid,
}

impl BallotBuilder {
    /// Create a new ballot builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the ballot number
    pub fn n(mut self, n: u64) -> Self {
        self.n = Some(n);
        self
    }

    /// Set the proposer node ID
    pub fn pid(mut self, pid: NodeId) -> Self {
        self.pid = Some(pid);
        self
    }

    /// Build the ballot with validation
    pub fn build(self) -> Result<Ballot, BallotBuildError> {
        let n = self.n.ok_or(BallotBuildError::MissingN)?;
        let pid = self.pid.ok_or(BallotBuildError::MissingPid)?;
        Ok(Ballot::new(n, pid))
    }
}

/// ## Definition 2.2.3: Hierarchical Actor Identifier
///
/// **Invariant**: Actor IDs form a tree structure where stopping a parent
/// automatically stops all descendants, preventing resource leaks.
///
/// **Format**: `platform.com/context_name/context_id/actor_name/actor_id`
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId {
    /// URL-like hierarchical identifier
    id: String,
}

impl ActorId {
    /// Private constructor - use ActorIdBuilder instead
    fn new(id: String) -> Self {
        Self { id }
    }

    /// Get the actor ID string
    pub fn id_str(&self) -> &str {
        &self.id
    }

    /// Get a cloned copy of the actor ID string
    pub fn id_string(&self) -> String {
        self.id.clone()
    }

    /// Create a child actor ID from this parent ID
    pub fn child(&self, child_name: impl Into<String>) -> ActorId {
        let child_name = child_name.into();
        let hierarchical_id = format!("{}/{}", self.id, child_name);
        ActorId::new(hierarchical_id)
    }

    /// Get the parent ID of this actor
    pub fn parent(&self) -> Option<ActorId> {
        let parts: Vec<&str> = self.id.split('/').collect();
        if parts.len() <= 1 {
            return None;
        }
        let parent_parts = &parts[..parts.len() - 1];
        let parent_id = parent_parts.join("/");
        Some(ActorId::new(parent_id))
    }

    /// Check if this actor is a child of the given parent
    pub fn is_child_of(&self, potential_parent: &ActorId) -> bool {
        self.id.starts_with(&format!("{}/", potential_parent.id))
    }

    /// Get all child actor IDs that would be affected by stopping this actor
    pub fn get_children(&self, all_actor_ids: &[ActorId]) -> Vec<ActorId> {
        all_actor_ids
            .iter()
            .filter(|id| id.is_child_of(self))
            .cloned()
            .collect()
    }

    /// Get the actor name (last part of the hierarchical ID)
    pub fn name(&self) -> &str {
        self.id.split('/').last().unwrap_or(&self.id)
    }
}

impl std::fmt::Display for ActorId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.id)
    }
}

impl Default for Ballot {
    fn default() -> Self {
        Self { n: 0, pid: 0 }
    }
}

/// Builder for ActorId with validation and fluent interface
#[derive(Debug, Default)]
pub struct ActorIdBuilder {
    id: Option<String>,
}

/// Error type for ActorId construction
#[derive(Debug, thiserror::Error)]
pub enum ActorIdBuildError {
    #[error("Actor ID string is required")]
    MissingId,
    #[error("Actor ID cannot be empty")]
    EmptyId,
    #[error("Invalid actor ID format: {reason}")]
    InvalidFormat { reason: String },
}

impl ActorIdBuilder {
    /// Create a new actor ID builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the actor ID string
    pub fn id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    /// Build from a parent actor ID and child name
    pub fn child_of(parent: &ActorId, child_name: impl Into<String>) -> Self {
        let child_name = child_name.into();
        let hierarchical_id = format!("{}/{}", parent.id_str(), child_name);
        Self {
            id: Some(hierarchical_id),
        }
    }

    /// Build the actor ID with validation
    pub fn build(self) -> Result<ActorId, ActorIdBuildError> {
        let id = self.id.ok_or(ActorIdBuildError::MissingId)?;

        if id.is_empty() {
            return Err(ActorIdBuildError::EmptyId);
        }

        // Additional validation could be added here
        // e.g., checking for valid URL format, forbidden characters, etc.

        Ok(ActorId::new(id))
    }
}

// ============================================================================
// SECTION 2.3: ERROR HANDLING AND RESULTS
// ============================================================================

/// ## Definition 2.3.1: System Error Types
///
/// **Completeness Property**: This enumeration covers all possible failure modes
/// in a distributed actor system, ensuring no unhandled error conditions.
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },

    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },

    #[error("Consensus timeout: {operation}")]
    ConsensusTimeout { operation: String },

    #[error("Platform initialization failed: {reason}")]
    PlatformInitFailed { reason: String },

    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },

    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Network error: {message}")]
    Network { message: String },

    #[error("Validation failed: {message}")]
    Validation { message: String },
}

/// ## Definition 2.3.2: Result Type
///
/// Standard result type providing consistent error handling across the system.
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// SECTION 2.4: EVENT SOURCING PRIMITIVES
// ============================================================================

/// ## Definition 2.4.1: Atomic Memory Operations
///
/// **ACID Property**: All operations in a single vector are applied atomically.
/// **Commutativity**: Operations on different keys commute, enabling parallel execution.
/// **Idempotency**: Reapplying the same operation sequence produces identical results.
#[derive(Debug, Clone)]
#[non_exhaustive]
pub enum MemoryOperation {
    /// Set a key-value pair
    Set { key: String, value: Vec<u8> },
    /// Delete a key
    Delete { key: String },
    /// Increment a counter (commutative)
    Increment { key: String, amount: i64 },
    /// Append to a list (order-preserving)
    Append { key: String, value: Vec<u8> },
    /// Remove from a list by index
    Remove { key: String, index: usize },
    /// Schedule a reaction for future execution
    Schedule {
        schedule_id: String,
        reaction_bytes: Vec<u8>,
        schedule: Schedule,
        actor_id: ActorId,
    },
    /// Cancel a scheduled reaction
    CancelSchedule { schedule_id: String },
}

/// ## Definition 2.4.2: Temporal Scheduling
///
/// **Determinism Property**: Given the same schedule and start time, execution
/// times are deterministic across all nodes in the distributed system.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum Schedule {
    /// Execute once at a specific time
    Once { at: SystemTime },
    /// Execute repeatedly at fixed intervals
    Interval {
        every: Duration,
        start_at: Option<SystemTime>,
        end_at: Option<SystemTime>,
    },
    /// Execute using cron-like expressions
    Cron {
        expression: String,
        end_at: Option<SystemTime>,
    },
}

/// Builder for Schedule with validation and fluent interface
#[derive(Debug)]
pub struct ScheduleBuilder {
    schedule_type: ScheduleType,
}

#[derive(Debug)]
enum ScheduleType {
    Once { at: SystemTime },
    Interval {
        every: Duration,
        start_at: Option<SystemTime>,
        end_at: Option<SystemTime>,
    },
    Cron {
        expression: String,
        end_at: Option<SystemTime>,
    },
}

/// Error type for Schedule construction
#[derive(Debug, thiserror::Error)]
pub enum ScheduleBuildError {
    #[error("Invalid cron expression: {expression}")]
    InvalidCronExpression { expression: String },
    #[error("Interval duration must be positive")]
    InvalidInterval,
    #[error("End time must be after start time")]
    InvalidTimeRange,
}

impl ScheduleBuilder {
    /// Create a one-time schedule for immediate execution
    pub fn now() -> Self {
        Self {
            schedule_type: ScheduleType::Once {
                at: SystemTime::now(),
            },
        }
    }

    /// Create a one-time schedule for execution after a delay
    pub fn after(delay: Duration) -> Self {
        Self {
            schedule_type: ScheduleType::Once {
                at: SystemTime::now() + delay,
            },
        }
    }

    /// Create a one-time schedule for execution at a specific time
    pub fn at(time: SystemTime) -> Self {
        Self {
            schedule_type: ScheduleType::Once { at: time },
        }
    }

    /// Create an interval schedule starting now
    pub fn every(interval: Duration) -> Self {
        Self {
            schedule_type: ScheduleType::Interval {
                every: interval,
                start_at: None,
                end_at: None,
            },
        }
    }

    /// Create an interval schedule with specific start time
    pub fn every_starting_at(interval: Duration, start_at: SystemTime) -> Self {
        Self {
            schedule_type: ScheduleType::Interval {
                every: interval,
                start_at: Some(start_at),
                end_at: None,
            },
        }
    }

    /// Create a cron-based schedule
    pub fn cron(expression: impl Into<String>) -> Self {
        Self {
            schedule_type: ScheduleType::Cron {
                expression: expression.into(),
                end_at: None,
            },
        }
    }

    /// Create a daily schedule at specific hour
    pub fn daily_at_hour(hour: u8) -> Self {
        Self::cron(format!("0 {} * * *", hour))
    }

    /// Create a weekly schedule
    pub fn weekly_on_day(day: u8, hour: u8) -> Self {
        Self::cron(format!("0 {} * * {}", hour, day))
    }

    /// Add an end time to any schedule
    pub fn until(mut self, end_time: SystemTime) -> Self {
        match &mut self.schedule_type {
            ScheduleType::Once { .. } => self, // One-time schedules ignore end time
            ScheduleType::Interval { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
            ScheduleType::Cron { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
        }
    }

    /// Build the schedule with validation
    pub fn build(self) -> Result<Schedule, ScheduleBuildError> {
        match self.schedule_type {
            ScheduleType::Once { at } => Ok(Schedule::Once { at }),
            ScheduleType::Interval {
                every,
                start_at,
                end_at,
            } => {
                if every.is_zero() {
                    return Err(ScheduleBuildError::InvalidInterval);
                }

                // Validate time range if both start and end are specified
                if let (Some(start), Some(end)) = (start_at, end_at) {
                    if end <= start {
                        return Err(ScheduleBuildError::InvalidTimeRange);
                    }
                }

                Ok(Schedule::Interval {
                    every,
                    start_at,
                    end_at,
                })
            }
            ScheduleType::Cron { expression, end_at } => {
                // Basic cron validation - in a real implementation, you'd use a cron parsing library
                if expression.trim().is_empty() {
                    return Err(ScheduleBuildError::InvalidCronExpression {
                        expression: expression.clone(),
                    });
                }

                Ok(Schedule::Cron { expression, end_at })
            }
        }
    }
}

// ============================================================================
// SECTION 2.5: ACTOR MEMORY ABSTRACTION
// ============================================================================

/// ## Definition 2.5.1: Actor Memory Interface
///
/// **Isolation Property**: Each actor can only access its own memory through
/// this interface, ensuring memory safety in concurrent environments.
///
/// **Read-Only Invariant**: Direct state mutation is impossible; all changes
/// must flow through the reaction system via `remember()` operations.
pub struct ActorMemories {
    // Implementation details are intentionally hidden to maintain abstraction
}

// ============================================================================
// SECTION 2.6: SUBSCRIPTION AND COMMUNICATION PRIMITIVES
// ============================================================================

/// ## Definition 2.6.1: Actor Subscription System
///
/// **Publish-Subscribe Pattern**: Actors can subscribe to reactions from other actors,
/// enabling loose coupling and event-driven communication patterns.
///
/// **Type Safety**: Subscriptions are type-safe - actors can only subscribe to
/// reactions they can handle through the RemoteReaction trait.
///
/// **Network Transparency**: Subscriptions work seamlessly across network boundaries,
/// with the system handling serialization, routing, and delivery automatically.

// ============================================================================
// SECTION 2.7: UNIFIED CONTEXT INTERFACE
// ============================================================================

/// ## Definition 2.7.1: Actor System Context
///
/// **Unification Theorem**: The Context provides a single interface for all
/// actor operations, eliminating the need for multiple communication patterns.
///
/// **Capability Security**: Actors can only perform operations they have
/// explicit capability for through the context interface.
pub struct Context {
    /// Memory access for the current actor
    pub memories: ActorMemories,
}

/// ## Definition 2.7.2: Action Builder Pattern
///
/// **Fluent Interface**: Provides type-safe method chaining for actor communication.
/// **Compile-Time Safety**: Invalid operation sequences are caught at compile time.
pub struct ActionBuilder<A: Action> {
    /// Type parameter ensures action-reaction type safety
    _phantom: PhantomData<A>,
}

// ============================================================================
// SECTION 2.8: BEHAVIORAL CONTRACTS (TRAITS)
// ============================================================================

/// ## Definition 2.8.1: Action Contract
///
/// **Pure Function Property**: Actions are pure functions that produce reactions
/// based solely on their input and the current context state.
///
/// **Determinism**: Given the same action and context state, the same reaction
/// is always produced, enabling distributed consensus.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type ensuring type safety
    type Reaction: Reaction;

    /// Execute the action with access to the unified context
    ///
    /// **Purity Constraint**: This method must not perform side effects.
    /// All state changes must be expressed through the returned reaction.
    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction>;

    /// Validate this action for security and business rules
    ///
    /// **Security Property**: This method enables capability-based security
    /// and role-based access control at the type system level.
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Definition 2.8.2: Reaction Contract
///
/// **Event Sourcing Theorem**: All state changes in the system are expressed
/// as sequences of memory operations, enabling perfect state reconstruction.
///
/// **Atomicity**: All operations returned by `remember()` are applied atomically.
pub trait Reaction: Send + Sync + 'static {
    /// Define the memory operations this reaction performs
    ///
    /// **Functional Purity**: This method must be pure - same input always
    /// produces the same sequence of memory operations.
    ///
    /// **Commutativity**: Operations on different keys can be reordered without
    /// affecting the final state, enabling distributed optimization.
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// ## Definition 2.8.3: Scheduled Reaction Contract
///
/// **Temporal Consistency**: Scheduled reactions maintain the same event sourcing
/// properties as regular reactions while adding temporal execution semantics.
///
/// **Lifecycle Management**: The system automatically manages actor lifecycle
/// for scheduled execution, ensuring reactions execute even when actors are stopped.
#[async_trait]
pub trait ScheduledReaction: Send + Sync + 'static {
    /// Define state changes for scheduled execution
    ///
    /// **Scheduling Invariant**: To schedule a reaction, include
    /// `MemoryOperation::Schedule` in the returned vector. This ensures
    /// scheduling follows the same event sourcing pattern as all other operations.
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to scheduled execution with full actor capabilities
    ///
    /// **Capability Preservation**: Scheduled reactions have the same capabilities
    /// as regular actions, including creating child actors and communicating
    /// with other actors in the system.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Definition 2.8.4: Remote Reaction Contract
///
/// **Distributed Communication**: Remote reactions handle incoming reactions from
/// other actors in the distributed system, enabling cross-actor communication
/// through the subscription mechanism.
///
/// **Network Transparency**: Remote reactions abstract away network details,
/// making distributed communication appear as local method calls.
///
/// **Serialization Safety**: Remote reactions work with rkyv-serialized data,
/// maintaining type safety across network boundaries.
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate incoming remote reaction data
    ///
    /// **Security Property**: This method enables validation of remote reactions
    /// before they are processed, preventing malicious or malformed data from
    /// affecting the actor's state.
    ///
    /// **Type Safety**: Validates that the serialized data can be safely
    /// deserialized into the expected type.
    fn validate(&self, reaction_bytes: &[u8]) -> TamtilResult<()>;

    /// Define state changes for remote reaction processing
    ///
    /// **Event Sourcing Consistency**: Remote reactions follow the same event
    /// sourcing pattern as local reactions, ensuring distributed consistency.
    ///
    /// **Atomicity**: All operations are applied atomically, maintaining
    /// ACID properties across distributed state changes.
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to incoming remote reaction with full actor capabilities
    ///
    /// **Distributed Capabilities**: Remote reactions can trigger actions on
    /// other actors, create child actors, and perform any operation available
    /// to local reactions.
    ///
    /// **Subscription Handling**: This method is automatically called when
    /// a subscribed reaction arrives from another actor in the system.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Definition 2.8.5: Actor Lifecycle Contract
///
/// **Identity Property**: Each actor has a unique identifier that remains
/// constant throughout its lifecycle.
///
/// **Hierarchical Cleanup**: When an actor stops, all its descendants are
/// automatically stopped, preventing resource leaks.
pub trait Actor: Send + Sync + 'static {
    /// Immutable reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_ballot_builder() {
        // Test successful construction
        let ballot = BallotBuilder::new()
            .n(42)
            .pid(123)
            .build()
            .expect("Should build successfully");

        assert_eq!(ballot.n(), 42);
        assert_eq!(ballot.pid(), 123);

        // Test missing fields
        let result = BallotBuilder::new().n(42).build();
        assert!(result.is_err());
    }

    #[test]
    fn test_actor_id_builder() {
        // Test successful construction
        let actor_id = ActorIdBuilder::new()
            .id("platform.com/context/actor")
            .build()
            .expect("Should build successfully");

        assert_eq!(actor_id.id_str(), "platform.com/context/actor");

        // Test child construction
        let child = ActorIdBuilder::child_of(&actor_id, "child_actor")
            .build()
            .expect("Should build child successfully");

        assert_eq!(child.id_str(), "platform.com/context/actor/child_actor");
        assert!(child.is_child_of(&actor_id));

        // Test empty ID
        let result = ActorIdBuilder::new().id("").build();
        assert!(result.is_err());
    }

    #[test]
    fn test_schedule_builder() {
        // Test interval schedule
        let schedule = ScheduleBuilder::every(Duration::from_secs(60))
            .build()
            .expect("Should build successfully");

        match schedule {
            Schedule::Interval { every, .. } => {
                assert_eq!(every, Duration::from_secs(60));
            }
            _ => panic!("Expected interval schedule"),
        }

        // Test cron schedule
        let schedule = ScheduleBuilder::daily_at_hour(9)
            .build()
            .expect("Should build successfully");

        match schedule {
            Schedule::Cron { expression, .. } => {
                assert_eq!(expression, "0 9 * * *");
            }
            _ => panic!("Expected cron schedule"),
        }

        // Test invalid interval
        let result = ScheduleBuilder::every(Duration::ZERO).build();
        assert!(result.is_err());
    }
}

// ============================================================================
// SECTION 3: MATHEMATICAL PROPERTIES AND GUARANTEES
// ============================================================================


