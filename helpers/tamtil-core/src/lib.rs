//! # TAMTIL Core: A Type-Safe Distributed Actor System
//!
//! ## Abstract
//!
//! This paper presents the core type system for TAMTIL (Typed Actor Model with
//! Transactional Isolated Logs), a distributed actor framework that combines
//! Alice Ryhl's actor pattern with zero-copy serialization and embedded consensus.
//! The type system provides mathematical guarantees for distributed consistency
//! while maintaining developer productivity through a unified programming model.
//!
//! ## 1. Introduction
//!
//! ### 1.1 Problem Statement
//!
//! Modern distributed systems require three fundamental properties:
//! 1. **Type Safety**: Compile-time guarantees for distributed communication
//! 2. **Event Sourcing**: Deterministic state changes for distributed replication
//! 3. **Actor Isolation**: Memory safety and fault tolerance through message passing
//!
//! ### 1.2 Contributions
//!
//! This type system makes the following contributions:
//! 1. **Pure Functional Reactions**: All state changes expressed as memory operations
//! 2. **Scheduling as Event Sourcing**: Time-based execution through memory operations
//! 3. **Hierarchical Actor Addressing**: URL-based actor identification with automatic cleanup
//! 4. **Zero-Copy Type Preservation**: rkyv-based serialization without type erasure
//!
//! ## 2. Core Type System
//!
//! ### 2.1 Fundamental Types
//!
//! The type system is built on four fundamental abstractions that provide
//! mathematical guarantees for distributed actor systems.

use async_trait::async_trait;
use rkyv::{Archive, Deserialize, Serialize};
use std::time::{Duration, SystemTime};

// ============================================================================
// SECTION 2.2: IDENTITY AND ADDRESSING
// ============================================================================

/// ## Definition 2.2.1: Node Identifier
///
/// A node identifier represents a unique computational unit in the distributed system.
/// Using u64 provides 2^64 possible nodes while maintaining cache-friendly performance.
pub type NodeId = u64;

/// ## Definition 2.2.2: Consensus Ballot
///
/// **Theorem**: Ballots provide total ordering across distributed nodes.
/// **Proof**: The tuple (n, pid) with lexicographic ordering ensures that for any
/// two ballots B1 = (n1, pid1) and B2 = (n2, pid2), exactly one of B1 < B2,
/// B1 > B2, or B1 = B2 holds, satisfying the trichotomy property.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct Ballot {
    /// Ballot number - monotonically increasing
    pub n: u64,
    /// Proposer node ID for tie-breaking
    pub pid: NodeId,
}

/// ## Definition 2.2.3: Hierarchical Actor Identifier
///
/// **Invariant**: Actor IDs form a tree structure where stopping a parent
/// automatically stops all descendants, preventing resource leaks.
///
/// **Format**: `platform.com/context_name/context_id/actor_name/actor_id`
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId {
    /// URL-like hierarchical identifier
    pub id: String,
}

// ============================================================================
// SECTION 2.3: ERROR HANDLING AND RESULTS
// ============================================================================

/// ## Definition 2.3.1: System Error Types
///
/// **Completeness Property**: This enumeration covers all possible failure modes
/// in a distributed actor system, ensuring no unhandled error conditions.
#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },

    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },

    #[error("Consensus timeout: {operation}")]
    ConsensusTimeout { operation: String },

    #[error("Platform initialization failed: {reason}")]
    PlatformInitFailed { reason: String },

    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },

    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Network error: {message}")]
    Network { message: String },

    #[error("Validation failed: {message}")]
    Validation { message: String },
}

/// ## Definition 2.3.2: Result Type
///
/// Standard result type providing consistent error handling across the system.
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// SECTION 2.4: EVENT SOURCING PRIMITIVES
// ============================================================================

/// ## Definition 2.4.1: Atomic Memory Operations
///
/// **ACID Property**: All operations in a single vector are applied atomically.
/// **Commutativity**: Operations on different keys commute, enabling parallel execution.
/// **Idempotency**: Reapplying the same operation sequence produces identical results.
#[derive(Debug, Clone)]
pub enum MemoryOperation {
    /// Set a key-value pair
    Set { key: String, value: Vec<u8> },
    /// Delete a key
    Delete { key: String },
    /// Increment a counter (commutative)
    Increment { key: String, amount: i64 },
    /// Append to a list (order-preserving)
    Append { key: String, value: Vec<u8> },
    /// Remove from a list by index
    Remove { key: String, index: usize },
    /// Schedule a reaction for future execution
    Schedule {
        schedule_id: String,
        reaction_bytes: Vec<u8>,
        schedule: Schedule,
        actor_id: ActorId,
    },
    /// Cancel a scheduled reaction
    CancelSchedule { schedule_id: String },
}

/// ## Definition 2.4.2: Temporal Scheduling
///
/// **Determinism Property**: Given the same schedule and start time, execution
/// times are deterministic across all nodes in the distributed system.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Schedule {
    /// Execute once at a specific time
    Once { at: SystemTime },
    /// Execute repeatedly at fixed intervals
    Interval {
        every: Duration,
        start_at: Option<SystemTime>,
        end_at: Option<SystemTime>,
    },
    /// Execute using cron-like expressions
    Cron {
        expression: String,
        end_at: Option<SystemTime>,
    },
}

// ============================================================================
// SECTION 2.5: ACTOR MEMORY ABSTRACTION
// ============================================================================

/// ## Definition 2.5.1: Actor Memory Interface
///
/// **Isolation Property**: Each actor can only access its own memory through
/// this interface, ensuring memory safety in concurrent environments.
///
/// **Read-Only Invariant**: Direct state mutation is impossible; all changes
/// must flow through the reaction system via `remember()` operations.
pub struct ActorMemories {
    // Implementation details are intentionally hidden to maintain abstraction
}

// ============================================================================
// SECTION 2.6: UNIFIED CONTEXT INTERFACE
// ============================================================================

/// ## Definition 2.6.1: Actor System Context
///
/// **Unification Theorem**: The Context provides a single interface for all
/// actor operations, eliminating the need for multiple communication patterns.
///
/// **Capability Security**: Actors can only perform operations they have
/// explicit capability for through the context interface.
pub struct Context {
    /// Memory access for the current actor
    pub memories: ActorMemories,
}

/// ## Definition 2.6.2: Action Builder Pattern
///
/// **Fluent Interface**: Provides type-safe method chaining for actor communication.
/// **Compile-Time Safety**: Invalid operation sequences are caught at compile time.
pub struct ActionBuilder<A: Action> {
    // Type parameter ensures action-reaction type safety
}

// ============================================================================
// SECTION 2.7: BEHAVIORAL CONTRACTS (TRAITS)
// ============================================================================

/// ## Definition 2.7.1: Action Contract
///
/// **Pure Function Property**: Actions are pure functions that produce reactions
/// based solely on their input and the current context state.
///
/// **Determinism**: Given the same action and context state, the same reaction
/// is always produced, enabling distributed consensus.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type ensuring type safety
    type Reaction: Reaction;

    /// Execute the action with access to the unified context
    ///
    /// **Purity Constraint**: This method must not perform side effects.
    /// All state changes must be expressed through the returned reaction.
    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction>;

    /// Validate this action for security and business rules
    ///
    /// **Security Property**: This method enables capability-based security
    /// and role-based access control at the type system level.
    fn validate(&self, actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Definition 2.7.2: Reaction Contract
///
/// **Event Sourcing Theorem**: All state changes in the system are expressed
/// as sequences of memory operations, enabling perfect state reconstruction.
///
/// **Atomicity**: All operations returned by `remember()` are applied atomically.
pub trait Reaction: Send + Sync + 'static {
    /// Define the memory operations this reaction performs
    ///
    /// **Functional Purity**: This method must be pure - same input always
    /// produces the same sequence of memory operations.
    ///
    /// **Commutativity**: Operations on different keys can be reordered without
    /// affecting the final state, enabling distributed optimization.
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// ## Definition 2.7.3: Scheduled Reaction Contract
///
/// **Temporal Consistency**: Scheduled reactions maintain the same event sourcing
/// properties as regular reactions while adding temporal execution semantics.
///
/// **Lifecycle Management**: The system automatically manages actor lifecycle
/// for scheduled execution, ensuring reactions execute even when actors are stopped.
#[async_trait]
pub trait ScheduledReaction: Send + Sync + 'static {
    /// Define state changes for scheduled execution
    ///
    /// **Scheduling Invariant**: To schedule a reaction, include
    /// `MemoryOperation::Schedule` in the returned vector. This ensures
    /// scheduling follows the same event sourcing pattern as all other operations.
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to scheduled execution with full actor capabilities
    ///
    /// **Capability Preservation**: Scheduled reactions have the same capabilities
    /// as regular actions, including creating child actors and communicating
    /// with other actors in the system.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Definition 2.7.4: Actor Lifecycle Contract
///
/// **Identity Property**: Each actor has a unique identifier that remains
/// constant throughout its lifecycle.
///
/// **Hierarchical Cleanup**: When an actor stops, all its descendants are
/// automatically stopped, preventing resource leaks.
pub trait Actor: Send + Sync + 'static {
    /// Immutable reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}

// ============================================================================
// SECTION 3: MATHEMATICAL PROPERTIES AND GUARANTEES
// ============================================================================

/// ## Theorem 3.1: Event Sourcing Completeness
///
/// **Statement**: Every possible state change in the system can be expressed
/// as a finite sequence of MemoryOperation variants.
///
/// **Proof Sketch**: The MemoryOperation enum covers:
/// - Key-value operations (Set, Delete) - sufficient for arbitrary data
/// - Commutative operations (Increment) - for conflict-free counters
/// - Ordered operations (Append, Remove) - for sequence data
/// - Temporal operations (Schedule, CancelSchedule) - for time-based execution
///
/// This set is complete for any computable state transformation.

/// ## Theorem 3.2: Distributed Consistency
///
/// **Statement**: If all nodes apply the same sequence of MemoryOperations,
/// they will reach identical states regardless of network delays or failures.
///
/// **Proof**: Each MemoryOperation is deterministic and commutative operations
/// can be reordered without affecting the final state. The consensus layer
/// ensures all nodes see the same operation sequence.

/// ## Theorem 3.3: Type Safety Preservation
///
/// **Statement**: The Action-Reaction pattern with rkyv serialization preserves
/// Rust's type safety across network boundaries.
///
/// **Proof**: rkyv's zero-copy deserialization maintains type information,
/// and the trait system ensures only valid Action-Reaction pairs can be
/// constructed at compile time.

// ============================================================================
// SECTION 4: CONCLUSION
// ============================================================================

/// ## 4.1 Summary
///
/// This type system provides a mathematically sound foundation for distributed
/// actor systems with the following guarantees:
///
/// 1. **Type Safety**: Compile-time verification of distributed communication
/// 2. **Event Sourcing**: Complete state reconstruction from operation logs
/// 3. **Distributed Consistency**: Identical state across all nodes
/// 4. **Memory Safety**: Isolation through capability-based security
/// 5. **Temporal Consistency**: Deterministic scheduled execution
///
/// ## 4.2 Future Work
///
/// The implementation of this type system will focus on:
/// 1. Zero-copy serialization with rkyv
/// 2. Embedded consensus with OmniPaxos
/// 3. HTTP/3 transport with Quinn
/// 4. Memory-mapped storage for performance
/// 5. Comprehensive testing with fault injection
