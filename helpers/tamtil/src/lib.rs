//! # TAMTIL: A Production-Ready Distributed Actor System
//!
//! ## Abstract
//!
//! This paper presents TAMTIL (Typed Actor Model with Transactional Isolated Logs), 
//! a novel distributed actor system that combines <PERSON> Ryhl's actor pattern with 
//! zero-copy serialization and embedded consensus. TAMTIL addresses the fundamental 
//! challenges of building fault-tolerant distributed systems while maintaining 
//! developer productivity through a unified programming model.
//!
//! ## 1. Introduction
//!
//! ### 1.1 Problem Statement
//!
//! Modern distributed systems face three critical challenges:
//! 1. **Performance vs Consistency Trade-off**: Traditional systems sacrifice 
//!    performance for consistency or vice versa
//! 2. **Complexity Explosion**: Different programming models for local vs 
//!    distributed execution increase cognitive load
//! 3. **Fault Tolerance Overhead**: Byzantine fault tolerance typically requires 
//!    significant performance penalties
//!
//! ### 1.2 Contributions
//!
//! TAMTIL makes the following novel contributions:
//! 1. **Unified Programming Model**: Identical developer experience for CLI and 
//!    web applications through transparent deployment modes
//! 2. **Zero-Copy Distributed Computing**: Leverages rkyv serialization for 
//!    near-zero overhead network communication
//! 3. **Hierarchical Actor Management**: Automatic resource cleanup through 
//!    parent-child actor relationships
//! 4. **Embedded Consensus**: OmniPaxos integration provides Byzantine fault 
//!    tolerance without external dependencies
//!
//! ## 2. System Architecture
//!
//! ### 2.1 Multi-Dimensional Design
//!
//! TAMTIL employs a three-dimensional architecture that scales from single 
//! processes to distributed clusters:
//!
//! #### Dimension 1: Hierarchical Organization
//! ```
//! Platform (Kubernetes-like Cluster)
//! ├── Context (Pod-like Container)
//! │   ├── Actor (Business Logic Unit)
//! │   ├── Actor (Child Actor)
//! │   │   └── Actor (Grandchild Actor)
//! │   └── Actor (Business Logic Unit)
//! └── Context (Pod-like Container)
//!     └── Actor (Business Logic Unit)
//! ```
//!
//! #### Dimension 2: Deployment Transparency
//! - **Local Mode**: Single-machine execution with direct memory communication
//! - **Distributed Mode**: Multi-machine execution with consensus-based replication
//!
//! #### Dimension 3: Communication Patterns
//! - **Action-Reaction**: Synchronous request-response with state changes
//! - **Subscription**: Asynchronous event-driven communication
//! - **Hierarchical**: Parent-child actor creation and management
//!
//! ### 2.2 Core Abstractions
//!
//! #### 2.2.1 Action Trait (Business Logic Encapsulation)
//!
//! Actions represent business operations that can be executed by actors:
//! ```rust
//! #[async_trait]
//! pub trait Action: Send + Sync + 'static {
//!     type Reaction: Reaction;
//!
//!     async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction>;
//!     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: Actions encapsulate business logic with access to the unified
//! Context (ctx) which provides intuitive access to all actor operations, memory, and
//! lifecycle management through a clean, stable API.
//!
//! #### 2.2.2 Reaction Trait (State Change Specification)
//!
//! Reactions define atomic state changes using event sourcing:
//! ```rust
//! pub trait Reaction: Send + Sync + 'static {
//!     fn remember(&self) -> Vec<MemoryOperation>;
//! }
//! ```
//!
//! **Design Rationale**: Pure functional state changes enable deterministic 
//! replay, distributed consensus, and time-travel debugging.
//!
//! #### 2.2.3 Context Interface (Unified Actor System)
//!
//! The Context (ctx) provides a single, intuitive interface for all actor operations:
//! ```rust
//! pub struct Context {
//!     pub memories: ActorMemories,
//!     // Internal actor management (hidden from developers)
//! }
//!
//! impl Context {
//!     // Actor lifecycle management
//!     async fn start<A: Actor>(&self, actor: A) -> TamtilResult<ActorId>;
//!     async fn stop(&self, actor_id: &ActorId) -> TamtilResult<()>;
//!
//!     // Actor communication with fluent API
//!     fn act<A: Action>(&self, action: A) -> ActionBuilder<A>;
//!     fn react(&self, reaction_type: impl Into<String>) -> ReactionBuilder;
//! }
//! ```
//!
//! **Design Rationale**: The unified Context makes actor operations feel natural:
//! - `ctx.start(actor)` for child actor creation
//! - `ctx.stop(&actor_id)` for child actor termination
//! - `ctx.act(action).in(&actor_id).await` for actor communication
//! - `ctx.react("EventType").from(&actor_id).await` for subscriptions
//!
//! ## 3. Event Sourcing and Memory Management
//!
//! ### 3.1 Memory Operations (Atomic State Primitives)
//!
//! TAMTIL provides five fundamental memory operations that are applied atomically:
//! ```rust
//! pub enum MemoryOperation {
//!     Set { key: String, value: Vec<u8> },      // Key-value storage
//!     Delete { key: String },                   // Key removal
//!     Increment { key: String, amount: i64 },   // Atomic counters
//!     Append { key: String, value: Vec<u8> },   // List operations
//!     Remove { key: String, index: usize },     // List element removal
//! }
//! ```
//!
//! **Design Rationale**: These operations cover the majority of state change 
//! patterns while maintaining atomicity guarantees essential for distributed 
//! consensus.
//!
//! ### 3.2 Actor Memories (Read-Only State Access)
//!
//! Actors access state through a read-only interface that prevents direct mutation:
//! ```rust
//! impl ActorMemories {
//!     async fn recall(&self, key: &str) -> TamtilResult<Option<Vec<u8>>>;
//!     async fn get_counter(&self, key: &str) -> TamtilResult<i64>;
//!     async fn get_list(&self, key: &str) -> TamtilResult<Vec<Vec<u8>>>;
//! }
//! ```
//!
//! **Design Rationale**: Read-only access ensures that all state changes flow 
//! through the reaction system, maintaining event sourcing invariants and 
//! enabling distributed replication.
//!
//! ## 4. Zero-Copy Serialization with rkyv
//!
//! ### 4.1 Performance Characteristics
//!
//! TAMTIL leverages rkyv for serialization, providing:
//! - **Zero-copy deserialization**: Access archived data without copying
//! - **Type preservation**: Full Rust type safety without type erasure
//! - **Validation**: Built-in data integrity verification
//! - **Performance**: 10-100x faster than serde for read-heavy workloads
//!
//! ### 4.2 Developer Requirements
//!
//! All actions and reactions must derive rkyv traits:
//! ```rust
//! #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
//! #[rkyv(derive(Debug))]
//! pub struct MyAction {
//!     field: String,
//! }
//! ```
//!
//! **Design Rationale**: Compile-time serialization contract ensures type safety 
//! and eliminates runtime serialization errors in distributed environments.
//!
//! ## 5. Distributed Consensus Integration
//!
//! ### 5.1 OmniPaxos Embedding
//!
//! TAMTIL embeds OmniPaxos consensus for reaction ordering:
//! ```
//! Action → Actor → Reaction → Consensus → Apply to All Nodes → Notify Subscribers
//! ```
//!
//! **Design Rationale**: Embedded consensus eliminates external dependencies 
//! while providing mathematical guarantees for Byzantine fault tolerance.
//!
//! ### 5.2 Consensus Scope
//!
//! Consensus operates at the reaction level, ensuring:
//! - **Reaction Ordering**: Consistent application order across all nodes
//! - **State Replication**: Identical state on all cluster members
//! - **Byzantine Fault Tolerance**: Handles up to (n-1)/3 malicious nodes
//!
//! ## 6. Hierarchical Actor Management
//!
//! ### 6.1 Dynamic Actor Creation
//!
//! Actors can create child actors and communicate with any actor in the platform:
//! ```rust
//! async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction> {
//!     // Call any actor in the platform
//!     let user_data = ctx.act(GetUserAction { id: self.user_id }).in(&user_id).await?;
//!
//!     // Subscribe to events from other actors
//!     ctx.react("UserUpdated").from(&notification_service).await?;
//!
//!     // Create child actors
//!     let worker = GenericActor::<WorkerAction>::new(ActorId::new("worker_1"));
//!     let child_id = ctx.start(worker).await?;
//!     // child_id becomes "parent_id/worker_1"
//!
//!     // Manage child actors
//!     ctx.stop(&child_id).await?;
//!
//!     // Access memory
//!     let existing_data = ctx.memories.recall("user_data").await?;
//!     ctx.memories.remember(vec![operation]).await?;
//! }
//! ```
//!
//! ### 6.2 Automatic Resource Cleanup
//!
//! When a parent actor stops, all descendant actors are automatically terminated:
//! ```
//! "manager" stops → "manager/worker_1" stops → "manager/worker_1/helper" stops
//! ```
//!
//! **Design Rationale**: Hierarchical cleanup prevents resource leaks and 
//! simplifies resource management in complex actor hierarchies.
//!
//! ## 7. Subscription System (Event-Driven Architecture)
//!
//! ### 7.1 Cross-Actor Communication
//!
//! Actors subscribe to reactions from other actors for event-driven coordination:
//! ```
//! 1. Actor A subscribes to Actor B's reactions
//! 2. Actor B produces a reaction
//! 3. Reaction delivered to Actor A
//! 4. RemoteReaction::validate() → remember() → react()
//! ```
//!
//! ### 7.2 RemoteReaction Processing
//!
//! Remote reactions follow a three-phase protocol:
//! ```rust
//! #[async_trait]
//! pub trait RemoteReaction: Send + Sync + 'static {
//!     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()>;
//!     fn remember(&self) -> Vec<MemoryOperation>;
//!     async fn react(&self, ctx: &Context) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: Three-phase processing ensures security (validate),
//! consistency (remember), and extensibility (react with full Context capabilities).
//!
//! #### 2.2.4 ScheduledReaction Trait (Time-Based Execution)
//!
//! Scheduled reactions enable time-based and interval-based execution:
//! ```rust
//! #[async_trait]
//! pub trait ScheduledReaction: Send + Sync + 'static {
//!     fn schedule(&self) -> Schedule;
//!     fn remember(&self) -> Vec<MemoryOperation>;
//!     async fn react(&self, ctx: &Context) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: Scheduled reactions provide cron-like functionality with
//! automatic actor lifecycle management. Actors are automatically started for
//! scheduled execution and restored to their previous state afterward.

//! ## STABLE DEVELOPER API
//!
//! The following types and traits are STABLE and guaranteed to remain backward
//! compatible within the same major version. These are the core interfaces that
//! developers use daily to build TAMTIL applications.

// ============================================================================
// STABLE API - CORE ERROR HANDLING
// ============================================================================

use rkyv::{Archive, Serialize, Deserialize};
use std::fmt;
use async_trait::async_trait;
use std::time::{Duration, SystemTime};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;

/// Node identifier type - simple u64 for maximum performance
pub type NodeId = u64;

/// ## Ballot Structure (Copied from OmniPaxos) - STABLE API
///
/// ### Mathematical Foundation: Lamport's Paxos
/// Ballots provide total ordering across distributed nodes. Higher ballot
/// numbers always take precedence, ensuring consensus safety properties.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct Ballot {
    /// Ballot number - monotonically increasing
    pub n: u64,
    /// Proposer node ID for tie-breaking
    pub pid: NodeId,
}

impl Default for Ballot {
    fn default() -> Self {
        Self { n: 0, pid: 0 }
    }
}

/// ## Actor Identifier - STABLE API
///
/// ### URL-Based Addressing with Interning
/// Actors are addressed using URL-like strings that are interned for performance.
/// Format: platform.com/context_name/context_id/actor_name/actor_id
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId {
    /// Interned string representation
    pub id: String,
}

impl ActorId {
    /// Create new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self { id: id.into() }
    }

    /// Create a child actor ID from a parent ID
    pub fn child(parent_id: &ActorId, child_name: impl Into<String>) -> Self {
        let child_name = child_name.into();
        let hierarchical_id = format!("{}/{}", parent_id.id, child_name);
        Self { id: hierarchical_id }
    }

    /// Get the parent ID of this actor
    pub fn parent(&self) -> Option<ActorId> {
        let parts: Vec<&str> = self.id.split('/').collect();
        if parts.len() <= 1 {
            return None;
        }
        let parent_parts = &parts[..parts.len() - 1];
        let parent_id = parent_parts.join("/");
        Some(ActorId { id: parent_id })
    }

    /// Check if this actor is a child of the given parent
    pub fn is_child_of(&self, potential_parent: &ActorId) -> bool {
        self.id.starts_with(&format!("{}/", potential_parent.id))
    }

    /// Get all child actor IDs that would be affected by stopping this actor
    pub fn get_children(&self, all_actor_ids: &[ActorId]) -> Vec<ActorId> {
        all_actor_ids
            .iter()
            .filter(|id| id.is_child_of(self))
            .cloned()
            .collect()
    }

    /// Get the actor name (last part of the hierarchical ID)
    pub fn name(&self) -> &str {
        self.id.split('/').last().unwrap_or(&self.id)
    }
}

impl fmt::Display for ActorId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.id)
    }
}

/// ## Error Types - STABLE API
///
/// ### Complete Error Handling
/// Production-ready error types covering all failure modes.
#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },

    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },

    #[error("Consensus timeout: {operation}")]
    ConsensusTimeout { operation: String },

    #[error("Platform initialization failed: {reason}")]
    PlatformInitFailed { reason: String },

    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },

    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Network error: {message}")]
    Network { message: String },

    #[error("Validation failed: {message}")]
    Validation { message: String },
}

/// Result type used throughout TAMTIL - STABLE API
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// STABLE API - MEMORY OPERATIONS (EVENT SOURCING)
// ============================================================================

/// ## Memory Operations (Event Sourcing) - STABLE API
///
/// ### Complete Memory Operations
/// All possible memory operations for event sourcing.
/// These operations are applied atomically when reactions are processed.
#[derive(Debug, Clone)]
pub enum MemoryOperation {
    /// Set a key-value pair
    Set { key: String, value: Vec<u8> },
    /// Delete a key
    Delete { key: String },
    /// Increment a counter
    Increment { key: String, amount: i64 },
    /// Append to a list
    Append { key: String, value: Vec<u8> },
    /// Remove from a list
    Remove { key: String, index: usize },
    /// Schedule a reaction for future execution
    Schedule {
        /// Unique identifier for this scheduled reaction
        schedule_id: String,
        /// Serialized scheduled reaction
        reaction_bytes: Vec<u8>,
        /// When/how often to execute
        schedule: Schedule,
        /// Actor ID that owns this scheduled reaction
        actor_id: ActorId,
    },
    /// Cancel a scheduled reaction
    CancelSchedule {
        /// ID of the scheduled reaction to cancel
        schedule_id: String
    },
}

/// ## Schedule Type - STABLE API
///
/// ### Time-Based Execution Scheduling
/// Defines when a scheduled reaction should be executed.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Schedule {
    /// Execute once at a specific time
    Once {
        /// When to execute the reaction
        at: SystemTime,
    },
    /// Execute repeatedly at fixed intervals
    Interval {
        /// How often to execute the reaction
        every: Duration,
        /// Optional start time (defaults to now)
        start_at: Option<SystemTime>,
        /// Optional end time (runs indefinitely if None)
        end_at: Option<SystemTime>,
    },
    /// Execute using cron-like expression
    Cron {
        /// Cron expression (e.g., "0 0 * * *" for daily at midnight)
        expression: String,
        /// Optional end time (runs indefinitely if None)
        end_at: Option<SystemTime>,
    },
}

impl Schedule {
    /// Create a one-time schedule for immediate execution
    pub fn now() -> Self {
        Schedule::Once {
            at: SystemTime::now(),
        }
    }

    /// Create a one-time schedule for execution after a delay
    pub fn after(delay: Duration) -> Self {
        Schedule::Once {
            at: SystemTime::now() + delay,
        }
    }

    /// Create an interval schedule starting now
    pub fn every(interval: Duration) -> Self {
        Schedule::Interval {
            every: interval,
            start_at: None,
            end_at: None,
        }
    }

    /// Create an interval schedule with specific start time
    pub fn every_starting_at(interval: Duration, start_at: SystemTime) -> Self {
        Schedule::Interval {
            every: interval,
            start_at: Some(start_at),
            end_at: None,
        }
    }

    /// Create a daily schedule at specific time
    pub fn daily_at_hour(hour: u8) -> Self {
        Schedule::Cron {
            expression: format!("0 {} * * *", hour),
            end_at: None,
        }
    }

    /// Create a weekly schedule
    pub fn weekly_on_day(day: u8, hour: u8) -> Self {
        Schedule::Cron {
            expression: format!("0 {} * * {}", hour, day),
            end_at: None,
        }
    }

    /// Add an end time to any schedule
    pub fn until(mut self, end_time: SystemTime) -> Self {
        match &mut self {
            Schedule::Once { .. } => self, // One-time schedules ignore end time
            Schedule::Interval { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
            Schedule::Cron { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
        }
    }
}

/// ## Actor Memories (Event Sourced Storage) - STABLE API
///
/// ### Complete Memory System
/// Production-ready event-sourced storage with ACID properties.
/// Provides read-only access to actor state for actions and reactions.
pub struct ActorMemories {
    /// Actor identifier
    actor_id: ActorId,
    /// Key-value storage
    data: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    /// Event log for reactions
    reactions: Arc<RwLock<Vec<Vec<u8>>>>,
    /// Counters
    counters: Arc<RwLock<HashMap<String, i64>>>,
    /// Lists
    lists: Arc<RwLock<HashMap<String, Vec<Vec<u8>>>>>,
}

impl ActorMemories {
    /// Create new actor memories
    pub fn new(actor_id: ActorId) -> Self {
        Self {
            actor_id,
            data: Arc::new(RwLock::new(HashMap::new())),
            reactions: Arc::new(RwLock::new(Vec::new())),
            counters: Arc::new(RwLock::new(HashMap::new())),
            lists: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Apply memory operations atomically - STABLE API
    pub async fn remember(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        for op in operations {
            match op {
                MemoryOperation::Set { key, value } => {
                    let mut data = self.data.write().await;
                    data.insert(key, value);
                }
                MemoryOperation::Delete { key } => {
                    let mut data = self.data.write().await;
                    data.remove(&key);
                }
                MemoryOperation::Increment { key, amount } => {
                    let mut counters = self.counters.write().await;
                    *counters.entry(key).or_insert(0) += amount;
                }
                MemoryOperation::Append { key, value } => {
                    let mut lists = self.lists.write().await;
                    lists.entry(key).or_insert_with(Vec::new).push(value);
                }
                MemoryOperation::Remove { key, index } => {
                    let mut lists = self.lists.write().await;
                    if let Some(list) = lists.get_mut(&key) {
                        if index < list.len() {
                            list.remove(index);
                        }
                    }
                }
                MemoryOperation::Schedule { schedule_id, reaction_bytes: _, schedule, actor_id } => {
                    // TODO: Forward to scheduler
                    println!("Scheduling reaction {} for actor {} with schedule {:?}",
                                 schedule_id, actor_id, schedule);
                }
                MemoryOperation::CancelSchedule { schedule_id } => {
                    // TODO: Forward to scheduler
                    println!("Cancelling scheduled reaction {}", schedule_id);
                }
            }
        }
        Ok(())
    }

    /// Recall a value by key - STABLE API
    pub async fn recall(&self, key: &str) -> TamtilResult<Option<Vec<u8>>> {
        let data = self.data.read().await;
        Ok(data.get(key).cloned())
    }

    /// Get counter value - STABLE API
    pub async fn get_counter(&self, key: &str) -> TamtilResult<i64> {
        let counters = self.counters.read().await;
        Ok(counters.get(key).copied().unwrap_or(0))
    }

    /// Get list - STABLE API
    pub async fn get_list(&self, key: &str) -> TamtilResult<Vec<Vec<u8>>> {
        let lists = self.lists.read().await;
        Ok(lists.get(key).cloned().unwrap_or_default())
    }

    /// Create a schedule memory operation - STABLE API
    pub fn create_schedule_operation(
        schedule_id: impl Into<String>,
        reaction_bytes: Vec<u8>,
        schedule: Schedule,
        actor_id: ActorId,
    ) -> MemoryOperation {
        MemoryOperation::Schedule {
            schedule_id: schedule_id.into(),
            reaction_bytes,
            schedule,
            actor_id,
        }
    }

    /// Cancel a scheduled reaction - STABLE API
    pub fn cancel_schedule(&self, schedule_id: impl Into<String>) -> MemoryOperation {
        MemoryOperation::CancelSchedule {
            schedule_id: schedule_id.into(),
        }
    }
}

impl Clone for ActorMemories {
    fn clone(&self) -> Self {
        Self {
            actor_id: self.actor_id.clone(),
            data: Arc::clone(&self.data),
            reactions: Arc::clone(&self.reactions),
            counters: Arc::clone(&self.counters),
            lists: Arc::clone(&self.lists),
        }
    }
}

// ============================================================================
// STABLE API - CONTEXT (UNIFIED ACTOR INTERFACE)
// ============================================================================

/// ## Context (ctx) - STABLE API
///
/// ### Unified Actor System Interface
/// The Context provides a single, intuitive interface for all actor operations.
/// This is the primary interface developers interact with in actions.
pub struct Context {
    /// Actor memory access
    pub memories: ActorMemories,
    // Internal actor management (hidden from developers)
}

impl Context {
    /// Create new context for an actor
    pub fn new(actor_id: ActorId) -> Self {
        Self {
            memories: ActorMemories::new(actor_id),
        }
    }

    /// Start a child actor - STABLE API
    pub async fn start<A: Actor>(&self, _actor: A) -> TamtilResult<ActorId> {
        // TODO: Implement actor creation
        Err(TamtilError::ActorNotFound {
            actor_id: "not_implemented".to_string(),
        })
    }

    /// Stop a child actor - STABLE API
    pub async fn stop(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        // TODO: Implement actor stopping
        Ok(())
    }

    /// Create action builder for actor communication - STABLE API
    pub fn act<A: Action>(&self, action: A) -> ActionBuilder<A> {
        ActionBuilder { action }
    }

    /// Subscribe to reactions from actor - STABLE API
    pub async fn subscribe_to_reactions(&self, _actor_id: &ActorId, _reaction_type: String) -> TamtilResult<()> {
        // TODO: Implement subscription
        Ok(())
    }
}

/// Action builder for fluent API - STABLE API
pub struct ActionBuilder<A: Action> {
    action: A,
}

impl<A: Action> ActionBuilder<A> {
    /// Execute action on target actor - STABLE API
    pub async fn in_actor(&self, _actor_id: &ActorId) -> TamtilResult<A::Reaction> {
        // TODO: Implement action execution
        Err(TamtilError::ActorNotFound {
            actor_id: "not_implemented".to_string(),
        })
    }
}

// ============================================================================
// STABLE API - CORE TRAITS (ACTION/REACTION PATTERN)
// ============================================================================

/// ## Action Trait (Alice Ryhl's Actor Pattern) - STABLE API
///
/// ### Core Action Interface
/// Actions contain business logic and produce reactions when executed.
/// This is the primary trait developers implement for business logic.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action with access to the unified context
    ///
    /// ### Parameters
    /// - `ctx`: Unified context providing access to all actor operations
    ///
    /// ### Returns
    /// Reaction that defines the state changes resulting from this action
    ///
    /// ### Design Philosophy
    /// Actions contain pure business logic. All state changes are expressed
    /// through the returned reaction, maintaining event sourcing principles.
    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction>;

    /// Validate this action for the given actor
    ///
    /// ### Parameters
    /// - `actor_id`: The actor that would execute this action
    ///
    /// ### Returns
    /// Ok(()) if action is valid, Err otherwise
    ///
    /// ### Security
    /// This method enables RBAC and other security validations before execution.
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Reaction Trait (Event Sourcing) - STABLE API
///
/// ### State Change Specification
/// Reactions define atomic state changes using event sourcing principles.
/// All state mutations in TAMTIL flow through reactions.
pub trait Reaction: Send + Sync + 'static {
    /// Define the memory operations this reaction performs
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    ///
    /// ### Event Sourcing
    /// This is the core of TAMTIL's event sourcing system. All state changes
    /// must be expressed as memory operations for consistency and replication.
    ///
    /// ### Example
    /// ```rust
    /// fn remember(&self) -> Vec<MemoryOperation> {
    ///     vec![
    ///         MemoryOperation::Set {
    ///             key: "user_email".to_string(),
    ///             value: self.new_email.as_bytes().to_vec(),
    ///         },
    ///         MemoryOperation::Increment {
    ///             key: "email_changes".to_string(),
    ///             amount: 1,
    ///         },
    ///     ]
    /// }
    /// ```
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// ## Scheduled Reaction Trait - STABLE API
///
/// ### Time-Based Reaction Execution
/// Allows reactions to be scheduled for execution at specific times or intervals.
/// Scheduling happens through memory operations to maintain event sourcing consistency.
#[async_trait]
pub trait ScheduledReaction: Send + Sync + 'static {
    /// Define state changes for scheduled execution
    ///
    /// ### Returns
    /// Vector of memory operations to apply when this reaction is scheduled
    ///
    /// ### Scheduling Pattern
    /// To schedule a reaction, include `MemoryOperation::Schedule` in the returned vector:
    /// ```rust
    /// fn remember(&self) -> Vec<MemoryOperation> {
    ///     vec![
    ///         // Your regular state changes
    ///         MemoryOperation::Set { key: "data".to_string(), value: self.data.clone() },
    ///         // Schedule this reaction
    ///         MemoryOperation::Schedule {
    ///             schedule_id: "my_schedule".to_string(),
    ///             reaction_bytes: rkyv::to_bytes(self).unwrap(),
    ///             schedule: Schedule::every(Duration::from_secs(3600)),
    ///             actor_id: self.actor_id.clone(),
    ///         }
    ///     ]
    /// }
    /// ```
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to this scheduled reaction execution
    ///
    /// ### Parameters
    /// - `ctx`: Unified context for all actor operations and memory access
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    ///
    /// ### Capabilities
    /// Scheduled reactions have full actor capabilities through the context.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Actor Trait - STABLE API
///
/// ### Actor Lifecycle Management
/// Defines the basic actor interface for lifecycle management.
pub trait Actor: Send + Sync + 'static {
    /// Reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}

// ============================================================================
// INTERNAL MODULES (IMPLEMENTATION DETAILS)
// ============================================================================

// These modules contain implementation details and may change between versions.
// Developers should only use the STABLE API defined above.

mod common_types;
pub mod consensus;
pub mod actor;
pub mod http;
pub mod platform;

// Re-export internal APIs for advanced use cases
// Note: These are NOT part of the stable API and may change
pub use consensus::{TamtilConsensus, ConsensusMessage, TamtilEntry};
pub use actor::{GenericActor, ActorHandle, SubscriptionManager, SubscribedReaction};
pub use http::{HttpServer, HttpRequest, HttpResponse};
pub use platform::{Platform, Context as PlatformContext};

